import {
    Geist_100Thin,
    Geist_200ExtraLight,
    Geist_300Light,
    Geist_400Regular,
    Geist_500Medium,
    Geist_600SemiBold,
    Geist_700Bold,
    Geist_800ExtraBold,
    Geist_900Black,
} from '@expo-google-fonts/geist';

export const fonts = {
  Geist_100Thin,
  Geist_200ExtraLight,
  Geist_300Light,
  Geist_400Regular,
  Geist_500Medium,
  Geist_600SemiBold,
  Geist_700Bold,
  Geist_800ExtraBold,
  Geist_900Black,
};

export const fontFamily = {
  thin: 'Geist_100Thin',
  extraLight: 'Geist_200ExtraLight',
  light: 'Geist_300Light',
  regular: 'Geist_400Regular',
  medium: 'Geist_500Medium',
  semiBold: 'Geist_600SemiBold',
  bold: 'Geist_700Bold',
  extraBold: 'Geist_800ExtraBold',
  black: 'Geist_900Black',
};

// Default font weights for easy usage
export const fontWeights = {
  100: fontFamily.thin,
  200: fontFamily.extraLight,
  300: fontFamily.light,
  400: fontFamily.regular,
  500: fontFamily.medium,
  600: fontFamily.semiBold,
  700: fontFamily.bold,
  800: fontFamily.extraBold,
  900: fontFamily.black,
}; 