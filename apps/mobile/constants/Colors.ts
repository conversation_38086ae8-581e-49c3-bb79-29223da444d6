/**
 * Below are the colors that are used in the app. The colors are defined in the light and dark mode.
 * There are many other ways to style your app. For example, [Nativewind](https://www.nativewind.dev/), [Tamagui](https://tamagui.dev/), [unistyles](https://reactnativeunistyles.vercel.app), etc.
 */

const tintColorLight = '#0a7ea4';
const tintColorDark = '#fff';

export const Colors = {
  light: {
 "background": "#f6f6f6",
    "foreground": "#1d1c1b",
    "card": "#f9fafb",
    "cardForeground": "#0a0a0a",
    "popover": "#fcfcfc",
    "popoverForeground": "#0a0a0a",
    "primary": "#171717",
    "primaryForeground": "#fafafa",
    "secondary": "#155dfc",
    "secondaryForeground": "#171717",
    "muted": "#e8e8e8",
    "mutedForeground": "#737373",
    "accent": "#0505050f",
    "accentForeground": "#0a0a0a",
    "destructive": "#e7000b",
    "destructiveForeground": "#e7000b",
    "border": "#05050514",
    "input": "#e5e5e5",
    "ring": "#a1a1a1",
    "chart1": "#f54900",
    "chart2": "#009689",
    "chart3": "#104e64",
    "chart4": "#ffb900",
    "chart5": "#fe9a00",
    "sidebar": "#f6f6f6",
    "sidebarForeground": "#0a0a0a",
    "sidebarPrimary": "#171717",
    "sidebarPrimaryForeground": "#fafafa",
    "sidebarAccent": "#0505050a",
    "sidebarAccentForeground": "#171717",
    "sidebarBorder": "#e5e5e5",
    "sidebarRing": "#a1a1a1"
},
  dark: {
    "background": "#121215",
    "foreground": "#fafafa",
    "card": "#161618",
    "cardForeground": "#fafafa",
    "popover": "#1b1c1f",
    "popoverForeground": "#fafafa",
    "primary": "#fafafa",
    "primaryForeground": "#171717",
    "secondary": "#155dfc",
    "secondaryForeground": "#fafafa",
    "muted": "#303030",
    "mutedForeground": "#a1a1a1",
    "accent": "#27272a",
    "accentForeground": "#f9fafb",
    "destructive": "#82181a",
    "destructiveForeground": "#fb2c36",
    "border": "#fcfcfc0f",
    "input": "#292929",
    "ring": "#525252",
    "chart1": "#1447e6",
    "chart2": "#00bc7d",
    "chart3": "#fe9a00",
    "chart4": "#ad46ff",
    "chart5": "#ff2056",
    "sidebar": "#121215",
    "sidebarForeground": "#fafafa",
    "sidebarPrimary": "#1447e6",
    "sidebarPrimaryForeground": "#fafafa",
    "sidebarAccent": "#262626",
    "sidebarAccentForeground": "#fafafa",
    "sidebarBorder": "#262626",
    "sidebarRing": "#525252"
}
}